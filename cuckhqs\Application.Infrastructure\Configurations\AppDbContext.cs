﻿using Application.Infrastructure.Entities;
using Application.Infrastructure.Entities.Employee;
using Application.Infrastructure.Entities.OrganizationUnit;
using Application.Infrastructure.Entities.User;
using Application.Infrastructure.Models;
using Fido2Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using ql_tb_vk_vt.Infrastructure.Configurations;
namespace Application.Infrastructure.Configurations
{
    public class AppDbContext : IdentityDbContext<ApplicationUser>
    {

        public DbSet<FidoStoredCredential> FidoStoredCredential => Set<FidoStoredCredential>();

        public DbSet<OrganizationUnitEntity> OrganizationUnits { get; set; }
        public DbSet<EmployeeCEntity> Employees { get; set; }
        public DbSet<WorkingScheduleEntity> WorkingSchedule { get; set; }
        public DbSet<WorkingScheduleCEntity> WorkingScheduleC { get; set; }
        public DbSet<WorkingScheduleEPEntity> WorkingScheduleEP { get; set; }
        public DbSet<WorkingScheduleEPHEntity> WorkingScheduleEPH { get; set; }
        public DbSet<WorkingScheduleOUEntity> WorkingScheduleOU{ get; set; }
        public DbSet<AdvertisementEntity> Advertisements { get; set; }
        public DbSet<WorkingResultEntity> WorkingResults { get; set; }
        public DbSet<OnDutyCommandEntity> OnDutyCommand { get; set; }
        public DbSet<WorkingResultSyntheticEntity> WorkingResultSynthetic { get; set; }
        public DbSet<WorkingScheduleIssueEntity> WorkingScheduleIssue { get; set; }
        public DbSet<WorkingScheduleIssueDetailEntity> WorkingScheduleIssueDetail { get; set; }
        public DbSet<RankEntity> Rank { get; set; }
        public DbSet<PositionEntity> Position { get; set; }
        public DbSet<AcademicRankEntity> AcademicRank { get; set; }
        public DbSet<DegreeEntity> Degree { get; set; }
        public DbSet<CountryEntity> Country { get; set; }
        public DbSet<ProvinceEntity> Province { get; set; }
        public DbSet<DistrictEntity> District { get; set; }
        public DbSet<WardEntity> Ward { get; set; }
        public DbSet<DecisionLevelEntity> DecisionLevel { get; set; }
        public DbSet<RewardTypeEntity> RewardType { get; set; }
        public DbSet<DisciplineTypeEntity> DisciplineType { get; set; }
        public DbSet<EducationLevelEntity> EducationLevel { get; set; }
        public DbSet<JournalTypeEntity> JournalType { get; set; }
        public DbSet<JournalGroupEntity> JournalGroup { get; set; }
        public DbSet<JournalEntity> Journal { get; set; }
        public DbSet<SoSEntity> SoS { get; set; }
        public DbSet<WorkingScheduleResultEntity> WorkingScheduleResult { get; set; }
        public DbSet<WorkingScheduleResult_AttachDetailEntity> WorkingScheduleResult_AttachDetail { get; set; }
        public DbSet<ScheduleEntity> ScheduleEntities { get; set; }
        

        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        public override int SaveChanges()
        {
            return base.SaveChanges();
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        => base.SaveChangesAsync(cancellationToken);

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<FidoStoredCredential>().HasKey(m => m.Id);

            // Configure OpenIddict entities
            modelBuilder.UseOpenIddict();

            modelBuilder.ApplyConfiguration(new DepartmentConfiguration());
            modelBuilder.ApplyConfiguration(new WorkingScheduleConfiguration());
            modelBuilder.ApplyConfiguration(new OrganizationUnitConfiguration());
            modelBuilder.ApplyConfiguration(new EmployeeConfiguration());
            modelBuilder.ApplyConfiguration(new WorkingScheduleEPConfiguration());
            modelBuilder.ApplyConfiguration(new WorkingScheduleEPHConfiguration());
            modelBuilder.ApplyConfiguration(new WorkingScheduleCConfiguration());
            modelBuilder.ApplyConfiguration(new AdvertisementConfiguration());
            modelBuilder.ApplyConfiguration(new WorkingResultConfiguration());
            modelBuilder.ApplyConfiguration(new OnDutyCommandConfiguration());
            modelBuilder.ApplyConfiguration(new WorkingResultSyntheticConfiguration());
            modelBuilder.ApplyConfiguration(new WorkingScheduleOUConfiguration());
            modelBuilder.ApplyConfiguration(new WorkingScheduleIssueConfiguration());
            modelBuilder.ApplyConfiguration(new WorkingScheduleIssueDetailConfiguration());
            modelBuilder.ApplyConfiguration(new ViewEmployeeConfiguration());
            modelBuilder.ApplyConfiguration(new RankConfiguration());
            modelBuilder.ApplyConfiguration(new PositionConfiguration());
            modelBuilder.ApplyConfiguration(new AcademicRankConfiguration());
            modelBuilder.ApplyConfiguration(new DegreeConfiguration());
            modelBuilder.ApplyConfiguration(new CountryConfiguration());
            modelBuilder.ApplyConfiguration(new ProvinceConfiguration());
            modelBuilder.ApplyConfiguration(new DistrictConfiguration());
            modelBuilder.ApplyConfiguration(new WardConfiguration());
            modelBuilder.ApplyConfiguration(new DecisionLevelConfiguration());
            modelBuilder.ApplyConfiguration(new RewardTypeConfiguration());
            modelBuilder.ApplyConfiguration(new DisciplineTypeConfiguration());
            modelBuilder.ApplyConfiguration(new EducationLevelConfiguration());
            modelBuilder.ApplyConfiguration(new JournalTypeConfiguration());
            modelBuilder.ApplyConfiguration(new JournalGroupConfiguration());
            modelBuilder.ApplyConfiguration(new JournalConfiguration());
            modelBuilder.ApplyConfiguration(new SosConfiguration());
            modelBuilder.ApplyConfiguration(new WorkingScheduleResultConfiguration());
            modelBuilder.ApplyConfiguration(new WorkingScheduleResult_AttachDetailConfiguration());
            modelBuilder.ApplyConfiguration(new ScheduleConfiguration());

            base.OnModelCreating(modelBuilder);
        }
    }
}
