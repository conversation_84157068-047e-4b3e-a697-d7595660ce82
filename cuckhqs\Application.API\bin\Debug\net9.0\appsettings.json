{"ConnectionStrings": {"DefaultConnection": "Server=*************;Database=msi_core;User Id=sa;Password=********;TrustServerCertificate=True;MultipleActiveResultSets=true;Connect Timeout=100"}, "OpenApiOAuth": {"TokenUrl": "http://localhost:44395/connect/token"}, "OpenIddictValidation": {"Issuer": "http://localhost:44395/"}, "AllowedOrigins": ["https://localhost:4200", "http://localhost:4200", "http://localhost:5095", "https://*************:4200", "http://*************:4200", "http://*************:5095", "http://localhost:6584", "https://localhost:6583"], "DeploySwaggerUI": true, "Serilog": {"Using": ["Serilog.Sinks.ApplicationInsights"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information"}}, "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "WriteTo": [{"Name": "ApplicationInsights", "Args": {"telemetryConverter": "Serilog.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"}}, {"Name": "File", "Args": {"path": "../../LogFiles/_logs-ResourceServer.txt", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level}] [{SourceContext}] [{EventId}] {Message}{NewLine}{Exception}", "rollOnFileSizeLimit": true, "fileSizeLimitBytes": 4194304, "retainedFileCountLimit": 5}}]}, "AllowedHosts": "*", "EmailSettings": {"Host": "localhost", "Port": 1025, "Username": "", "Password": "", "SenderEmail": "<EMAIL>", "SenderName": "My App"}}