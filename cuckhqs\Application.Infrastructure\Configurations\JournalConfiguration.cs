﻿using Application.Infrastructure.Entities;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Application.Infrastructure.Configurations
{
    public class JournalConfiguration : IEntityTypeConfiguration<JournalEntity>
    {
        public void Configure(EntityTypeBuilder<JournalEntity> builder)
        {
            builder.ToTable("Journal");
            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id).HasColumnName("ID");
            builder.Property(x => x.JournalCode).HasColumnName("JournalCode");
            builder.Property(x => x.JournalName).HasColumnName("JournalName");
            builder.Property(x => x.ISSN).HasColumnName("ISSN");
            builder.Property(x => x.JournalTypeId).HasColumnName("JournalTypeId");
            builder.Property(x => x.JournalTypeCode).HasColumnName("JournalTypeCode");
            builder.Property(x => x.JournalTypeId_AN).HasColumnName("JournalTypeId_AN");
            builder.Property(x => x.JournalGroupId).HasColumnName("JournalGroupId");
            builder.Property(x => x.JournalGroupCode).HasColumnName("JournalGroupCode");
            builder.Property(x => x.JournalGroupId_AN).HasColumnName("JournalGroupId_AN");
            builder.Property(x => x.JournalSpecialCode).HasColumnName("JournalSpecialCode");
            builder.Property(x => x.PublishingAgency).HasColumnName("PublishingAgency");
            builder.Property(x => x.PointFrom).HasColumnName("PointFrom").HasPrecision(18, 2);
            builder.Property(x => x.PointTo).HasColumnName("PointTo").HasPrecision(18, 2);
            builder.Property(x => x.Description).HasColumnName("Description");
            builder.Property(x => x.Active).HasColumnName("Active");
            builder.Property(x => x.SortOrder).HasColumnName("SortOrder");
            builder.Property(x => x.CreatedDate).HasColumnName("CreatedDate");
            builder.Property(x => x.ModifiedDate).HasColumnName("ModifiedDate");
            builder.Property(x => x.IPAddress).HasColumnName("IPAddress");
            builder.Property(x => x.ModifiedBy).HasColumnName("ModifiedBy");
            builder.Property(x => x.CreatedBy).HasColumnName("CreatedBy");
        }
    }
}
